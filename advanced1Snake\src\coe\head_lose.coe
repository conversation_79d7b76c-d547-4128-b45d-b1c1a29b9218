; Sample COE file with 8-bit data width
memory_initialization_radix=2;
memory_initialization_vector=
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
10011111,
10011111,
01001011,
00100000,
01001010,
01101010,
01101001,
01101001,
01101010,
01101001,
01101001,
01101001,
01100001,
01101100,
10011111,
10011111,
00101010,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
10011111,
10011111,
01110100,
01101001,
10001001,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010001,
10110011,
10011111,
10011111,
01001011,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
10011111,
10011111,
10110100,
11111001,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111001,
11111011,
10011111,
10011111,
10010100,
01100001,
00100001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100000,
01101010,
10011111,
10011111,
11011100,
11111001,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11011100,
10011111,
10011111,
11011100,
11010001,
01101010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100000,
01101001,
10110010,
10011111,
01111111,
11011100,
11111001,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11011100,
10011111,
10011111,
11011100,
11111001,
11110010,
01101010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100000,
01101010,
10110010,
11111011,
10011111,
01111111,
11011100,
11111001,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11011100,
10011111,
10011111,
11011100,
11111001,
11111010,
11111010,
01101010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
01001010,
10101010,
11111010,
11111011,
10011111,
01111111,
11011100,
11111001,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11011100,
10011111,
10011111,
11011100,
11111001,
11111010,
11111010,
11011010,
01101010,
00100001,
00000000,
00000000,
00000000,
00000000,
01000001,
10001010,
11111010,
11111010,
11111011,
10011111,
01111111,
11011100,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11011100,
10011111,
10011111,
11011100,
11111010,
11111010,
11111010,
11111010,
11010010,
01101010,
00000000,
00000000,
00000000,
00000000,
01101010,
11010010,
11111010,
11111010,
11111011,
10011111,
10011111,
11011100,
11110001,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11110010,
10101001,
11010011,
10011111,
10011111,
11011100,
11110001,
11110010,
11110010,
11110010,
11110010,
10001010,
01000001,
00000000,
00000000,
01000001,
10001010,
11111010,
11111010,
11111010,
11111011,
10011111,
10011111,
11011100,
10100001,
11001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10101010,
10101001,
11011011,
10011111,
10011111,
11011100,
11110001,
11110010,
11110010,
11110010,
11110010,
11010010,
01101010,
00000000,
00000000,
01001001,
11010010,
11110010,
11110010,
11110010,
11110011,
10011111,
10011111,
11011100,
11010001,
10100001,
11010010,
11111010,
11111010,
11111010,
11110010,
10101010,
10100010,
11110010,
11111100,
10011111,
10011111,
11011100,
11110001,
11110010,
11110010,
11110010,
11110010,
11110010,
10001010,
00100001,
00000000,
01101010,
11110010,
11110010,
11110010,
11110010,
11110011,
10011111,
10011111,
11011100,
11111001,
11001010,
10100001,
10101010,
11001010,
10101010,
10100001,
10101010,
11110010,
11111010,
11011100,
10011111,
10011111,
11011100,
11110001,
11110010,
11110010,
11110010,
11110010,
11110010,
10101001,
01101010,
00100001,
10001010,
11110010,
11110010,
11110010,
11110010,
11110011,
10011111,
10011111,
11011101,
11111001,
11111010,
11010010,
11001001,
10101010,
10101010,
11010010,
11111010,
11111010,
11111001,
11011100,
10011111,
10011111,
11011100,
11110001,
11110010,
11110010,
11110010,
11110010,
11110010,
11010010,
01101010,
01000001,
10101010,
11110010,
11110010,
11110010,
11110010,
11110011,
10011111,
10011111,
10111101,
11111001,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11111001,
11011100,
10011111,
10011111,
11011100,
11110001,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
01101010,
01001010,
10101010,
11110010,
11110010,
11010010,
11010010,
11010011,
10011111,
10011111,
10111101,
11111001,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11111010,
11011100,
10011111,
10011111,
11011100,
11110001,
11010010,
10101010,
10110010,
11110010,
11110010,
11110010,
01101010,
01101010,
10101001,
11111010,
10101001,
01000001,
01101001,
01101001,
01101011,
01110100,
10010011,
11010010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11010010,
10110011,
01110100,
01101011,
01101010,
01101001,
01000001,
01000001,
01101010,
11110010,
11110010,
11110010,
01101001,
01101010,
10101010,
11110010,
11110010,
10101010,
10001010,
01101010,
01101001,
01100001,
01000001,
01000001,
01101001,
11110010,
11110010,
11110010,
11110010,
11110010,
11010010,
01101001,
01000001,
01000001,
01100001,
01101001,
01101001,
10001010,
10101010,
10110010,
11110010,
11110010,
11110010,
11111010,
01101010,
00100001,
10110010,
11111010,
11110010,
11110010,
11110010,
11111010,
11111010,
11010010,
11010010,
10110010,
10110010,
11111010,
11111010,
11110010,
11111010,
11111010,
11111010,
10001010,
10101010,
10110010,
11010010,
11111010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11111010,
01101010,
00100001,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
01101010,
00100000,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10110001,
01101010,
00000000,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
00100001,
00000000,
01000001,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
01101010,
00000000,
00000000,
00100000,
01101010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10101010,
01000001,
00000000,
00000000,
00000000,
01000001,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
01101010,
00100000,
00000000,
00000000,
00000000,
00000000,
01101010,
10110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01001010,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01101010,
10110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01001010,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01101010,
10001010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10110010,
01101010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
01000001,
01101010,
10001010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
10110010,
01101010,
01001010,
00100001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
01000001,
01001010,
01101010,
10001010,
10001010,
10110010,
10110010,
10110010,
10001010,
10001010,
01101001,
01000001,
00100001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
00100001,
01000001,
01000001,
00100001,
00100001,
00100000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000;
