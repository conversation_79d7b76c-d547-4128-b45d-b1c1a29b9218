set_property IOSTANDARD LVCMOS33 [get_ports clock]
set_property PACKAGE_PIN Y18 [get_ports clock]

set_property IOSTANDARD LVCMOS33 [get_ports reset]
set_property PACKAGE_PIN P20 [get_ports reset]

set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[4]}]
set_property PACKAGE_PIN R1 [get_ports {raw_button[0]}]
set_property PACKAGE_PIN P1 [get_ports {raw_button[1]}]
set_property PACKAGE_PIN P5 [get_ports {raw_button[2]}]
set_property PACKAGE_PIN P4 [get_ports {raw_button[3]}]
set_property PACKAGE_PIN P2 [get_ports {raw_button[4]}]

set_property IOSTANDARD LVCMOS33 [get_ports lcd_screen_adj]
set_property PACKAGE_PIN B16 [get_ports lcd_screen_adj]

set_property IOSTANDARD LVCMOS33 [get_ports lcd_screen_mode]
set_property PACKAGE_PIN C18 [get_ports lcd_screen_mode]

set_property IOSTANDARD LVCMOS33 [get_ports lcd_screen_de]
set_property PACKAGE_PIN C20 [get_ports lcd_screen_de]

set_property IOSTANDARD LVCMOS33 [get_ports lcd_screen_dclk]
set_property PACKAGE_PIN D20 [get_ports lcd_screen_dclk]

set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_r[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_r[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_r[2]}]
set_property PACKAGE_PIN G17 [get_ports {lcd_screen_r[0]}]
set_property PACKAGE_PIN G18 [get_ports {lcd_screen_r[1]}]
set_property PACKAGE_PIN J15 [get_ports {lcd_screen_r[2]}]

set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_g[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_g[1]}]
set_property PACKAGE_PIN H17 [get_ports {lcd_screen_g[0]}]
set_property PACKAGE_PIN H18 [get_ports {lcd_screen_g[1]}]

set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_b[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_b[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {lcd_screen_b[2]}]
set_property PACKAGE_PIN H20 [get_ports {lcd_screen_b[0]}]
set_property PACKAGE_PIN G20 [get_ports {lcd_screen_b[1]}]
set_property PACKAGE_PIN K21 [get_ports {lcd_screen_b[2]}]

set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[6]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[5]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[4]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports digital_tube_dp_n]
set_property PACKAGE_PIN F15 [get_ports {digital_tube_segment_n[6]}]
set_property PACKAGE_PIN F13 [get_ports {digital_tube_segment_n[5]}]
set_property PACKAGE_PIN F14 [get_ports {digital_tube_segment_n[4]}]
set_property PACKAGE_PIN F16 [get_ports {digital_tube_segment_n[3]}]
set_property PACKAGE_PIN E17 [get_ports {digital_tube_segment_n[2]}]
set_property PACKAGE_PIN C14 [get_ports {digital_tube_segment_n[1]}]
set_property PACKAGE_PIN C15 [get_ports {digital_tube_segment_n[0]}]
set_property PACKAGE_PIN E13 [get_ports digital_tube_dp_n]

set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[4]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[5]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[6]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[7]}]
set_property PACKAGE_PIN C19 [get_ports {digital_tube_enable_n[0]}]
set_property PACKAGE_PIN E19 [get_ports {digital_tube_enable_n[1]}]
set_property PACKAGE_PIN D19 [get_ports {digital_tube_enable_n[2]}]
set_property PACKAGE_PIN F18 [get_ports {digital_tube_enable_n[3]}]
set_property PACKAGE_PIN E18 [get_ports {digital_tube_enable_n[4]}]
set_property PACKAGE_PIN B20 [get_ports {digital_tube_enable_n[5]}]
set_property PACKAGE_PIN A20 [get_ports {digital_tube_enable_n[6]}]
set_property PACKAGE_PIN A18 [get_ports {digital_tube_enable_n[7]}]

set_property IOSTANDARD LVCMOS33 [get_ports buzzer_output]
set_property PACKAGE_PIN A19 [get_ports buzzer_output]
