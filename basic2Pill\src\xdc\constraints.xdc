set_property IOSTANDARD LVCMOS33 [get_ports clock]
set_property PACKAGE_PIN Y18 [get_ports clock]

set_property IOSTANDARD LVCMOS33 [get_ports reset]
set_property PACKAGE_PIN P20 [get_ports reset]

set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_col_n[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_col_n[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_col_n[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_col_n[3]}]
set_property PACKAGE_PIN L5 [get_ports {keyboard_col_n[0]}]
set_property PACKAGE_PIN J6 [get_ports {keyboard_col_n[1]}]
set_property PACKAGE_PIN K6 [get_ports {keyboard_col_n[2]}]
set_property PACKAGE_PIN M2 [get_ports {keyboard_col_n[3]}]

set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_row_n[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_row_n[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_row_n[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {keyboard_row_n[3]}]
set_property PACKAGE_PIN K3 [get_ports {keyboard_row_n[0]}]
set_property PACKAGE_PIN L3 [get_ports {keyboard_row_n[1]}]
set_property PACKAGE_PIN J4 [get_ports {keyboard_row_n[2]}]
set_property PACKAGE_PIN K4 [get_ports {keyboard_row_n[3]}]

set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {raw_button[4]}]
set_property PACKAGE_PIN R1 [get_ports {raw_button[0]}]
set_property PACKAGE_PIN P1 [get_ports {raw_button[1]}]
set_property PACKAGE_PIN P5 [get_ports {raw_button[2]}]
set_property PACKAGE_PIN P4 [get_ports {raw_button[3]}]
set_property PACKAGE_PIN P2 [get_ports {raw_button[4]}]

set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[6]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[5]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[4]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_segment_n[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports digital_tube_dp_n]
set_property PACKAGE_PIN F15 [get_ports {digital_tube_segment_n[6]}]
set_property PACKAGE_PIN F13 [get_ports {digital_tube_segment_n[5]}]
set_property PACKAGE_PIN F14 [get_ports {digital_tube_segment_n[4]}]
set_property PACKAGE_PIN F16 [get_ports {digital_tube_segment_n[3]}]
set_property PACKAGE_PIN E17 [get_ports {digital_tube_segment_n[2]}]
set_property PACKAGE_PIN C14 [get_ports {digital_tube_segment_n[1]}]
set_property PACKAGE_PIN C15 [get_ports {digital_tube_segment_n[0]}]
set_property PACKAGE_PIN E13 [get_ports digital_tube_dp_n]

set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[0]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[1]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[2]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[3]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[4]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[5]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[6]}]
set_property IOSTANDARD LVCMOS33 [get_ports {digital_tube_enable_n[7]}]
set_property PACKAGE_PIN C19 [get_ports {digital_tube_enable_n[0]}]
set_property PACKAGE_PIN E19 [get_ports {digital_tube_enable_n[1]}]
set_property PACKAGE_PIN D19 [get_ports {digital_tube_enable_n[2]}]
set_property PACKAGE_PIN F18 [get_ports {digital_tube_enable_n[3]}]
set_property PACKAGE_PIN E18 [get_ports {digital_tube_enable_n[4]}]
set_property PACKAGE_PIN B20 [get_ports {digital_tube_enable_n[5]}]
set_property PACKAGE_PIN A20 [get_ports {digital_tube_enable_n[6]}]
set_property PACKAGE_PIN A18 [get_ports {digital_tube_enable_n[7]}]

set_property IOSTANDARD LVCMOS33 [get_ports buzzer_audio]
set_property PACKAGE_PIN A19 [get_ports buzzer_audio]

set_property IOSTANDARD LVCMOS33 [get_ports strict_enable]
set_property PACKAGE_PIN W4 [get_ports strict_enable]

set_property IOSTANDARD LVCMOS33 [get_ports funnel_disable]
set_property PACKAGE_PIN N19 [get_ports funnel_disable]

set_property IOSTANDARD LVCMOS33 [get_ports motor_enable]
set_property PACKAGE_PIN J17 [get_ports motor_enable]
