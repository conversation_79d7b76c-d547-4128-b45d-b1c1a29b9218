{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "clock_generator", "component_reference": "xilinx.com:ip:clk_wiz:6.0", "ip_revision": "14", "gen_directory": "../../../../advanced1Snake.gen/sources_1/ip/clock_generator", "parameters": {"component_parameters": {"Component_Name": [{"value": "clock_generator", "resolve_type": "user", "usage": "all"}], "USER_CLK_FREQ0": [{"value": "100.0", "resolve_type": "user", "format": "float", "usage": "all"}], "USER_CLK_FREQ1": [{"value": "100.0", "resolve_type": "user", "format": "float", "usage": "all"}], "USER_CLK_FREQ2": [{"value": "100.0", "resolve_type": "user", "format": "float", "usage": "all"}], "USER_CLK_FREQ3": [{"value": "100.0", "resolve_type": "user", "format": "float", "usage": "all"}], "ENABLE_CLOCK_MONITOR": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "OPTIMIZE_CLOCKING_STRUCTURE_EN": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ENABLE_USER_CLOCK0": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ENABLE_USER_CLOCK1": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ENABLE_USER_CLOCK2": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ENABLE_USER_CLOCK3": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Enable_PLL0": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Enable_PLL1": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "REF_CLK_FREQ": [{"value": "100.0", "resolve_type": "user", "format": "float", "usage": "all"}], "PRECISION": [{"value": "1", "resolve_type": "user", "format": "float", "usage": "all"}], "PRIMITIVE": [{"value": "MMCM", "resolve_type": "user", "usage": "all"}], "PRIMTYPE_SEL": [{"value": "mmcm_adv", "resolve_type": "user", "usage": "all"}], "CLOCK_MGR_TYPE": [{"value": "auto", "resolve_type": "user", "usage": "all"}], "USE_FREQ_SYNTH": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_SPREAD_SPECTRUM": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_PHASE_ALIGNMENT": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_MIN_POWER": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_DYN_PHASE_SHIFT": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_DYN_RECONFIG": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "JITTER_SEL": [{"value": "No_Jitter", "resolve_type": "user", "usage": "all"}], "PRIM_IN_FREQ": [{"value": "100.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PRIM_IN_TIMEPERIOD": [{"value": "10.000", "resolve_type": "user", "format": "float", "usage": "all"}], "IN_FREQ_UNITS": [{"value": "Units_MHz", "resolve_type": "user", "usage": "all"}], "PHASESHIFT_MODE": [{"value": "WAVEFORM", "resolve_type": "user", "usage": "all"}], "IN_JITTER_UNITS": [{"value": "Units_UI", "resolve_type": "user", "usage": "all"}], "RELATIVE_INCLK": [{"value": "REL_PRIMARY", "resolve_type": "user", "usage": "all"}], "USE_INCLK_SWITCHOVER": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "SECONDARY_IN_FREQ": [{"value": "100.000", "resolve_type": "user", "format": "float", "usage": "all"}], "SECONDARY_IN_TIMEPERIOD": [{"value": "10.000", "resolve_type": "user", "format": "float", "usage": "all"}], "SECONDARY_PORT": [{"value": "clk_in2", "resolve_type": "user", "usage": "all"}], "SECONDARY_SOURCE": [{"value": "Single_ended_clock_capable_pin", "resolve_type": "user", "usage": "all"}], "JITTER_OPTIONS": [{"value": "UI", "resolve_type": "user", "usage": "all"}], "CLKIN1_UI_JITTER": [{"value": "0.010", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKIN2_UI_JITTER": [{"value": "0.010", "resolve_type": "user", "format": "float", "usage": "all"}], "PRIM_IN_JITTER": [{"value": "0.010", "resolve_type": "user", "format": "float", "usage": "all"}], "SECONDARY_IN_JITTER": [{"value": "0.010", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKIN1_JITTER_PS": [{"value": "100.0", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKIN2_JITTER_PS": [{"value": "100.0", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT1_USED": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT2_USED": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT3_USED": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT4_USED": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT5_USED": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT6_USED": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT7_USED": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "NUM_OUT_CLKS": [{"value": "6", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "CLK_OUT1_USE_FINE_PS_GUI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLK_OUT2_USE_FINE_PS_GUI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLK_OUT3_USE_FINE_PS_GUI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLK_OUT4_USE_FINE_PS_GUI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLK_OUT5_USE_FINE_PS_GUI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLK_OUT6_USE_FINE_PS_GUI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLK_OUT7_USE_FINE_PS_GUI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "PRIMARY_PORT": [{"value": "clock_in", "value_src": "user", "resolve_type": "user", "usage": "all"}], "CLK_OUT1_PORT": [{"value": "controller_phase0", "value_src": "user", "resolve_type": "user", "usage": "all"}], "CLK_OUT2_PORT": [{"value": "controller_phase1", "value_src": "user", "resolve_type": "user", "usage": "all"}], "CLK_OUT3_PORT": [{"value": "controller_phase2", "value_src": "user", "resolve_type": "user", "usage": "all"}], "CLK_OUT4_PORT": [{"value": "controller_phase3", "value_src": "user", "resolve_type": "user", "usage": "all"}], "CLK_OUT5_PORT": [{"value": "lcd_screen_dclk", "value_src": "user", "resolve_type": "user", "usage": "all"}], "CLK_OUT6_PORT": [{"value": "clock_out", "value_src": "user", "resolve_type": "user", "usage": "all"}], "CLK_OUT7_PORT": [{"value": "clk_out7", "resolve_type": "user", "usage": "all"}], "DADDR_PORT": [{"value": "daddr", "resolve_type": "user", "usage": "all"}], "DCLK_PORT": [{"value": "dclk", "resolve_type": "user", "usage": "all"}], "DRDY_PORT": [{"value": "drdy", "resolve_type": "user", "usage": "all"}], "DWE_PORT": [{"value": "dwe", "resolve_type": "user", "usage": "all"}], "DIN_PORT": [{"value": "din", "resolve_type": "user", "usage": "all"}], "DOUT_PORT": [{"value": "dout", "resolve_type": "user", "usage": "all"}], "DEN_PORT": [{"value": "den", "resolve_type": "user", "usage": "all"}], "PSCLK_PORT": [{"value": "psclk", "resolve_type": "user", "usage": "all"}], "PSEN_PORT": [{"value": "psen", "resolve_type": "user", "usage": "all"}], "PSINCDEC_PORT": [{"value": "psincdec", "resolve_type": "user", "usage": "all"}], "PSDONE_PORT": [{"value": "psdone", "resolve_type": "user", "usage": "all"}], "CLKOUT1_REQUESTED_OUT_FREQ": [{"value": "25", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT1_REQUESTED_PHASE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT1_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT2_REQUESTED_OUT_FREQ": [{"value": "25", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT2_REQUESTED_PHASE": [{"value": "90", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT2_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT3_REQUESTED_OUT_FREQ": [{"value": "25", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT3_REQUESTED_PHASE": [{"value": "180", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT3_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT4_REQUESTED_OUT_FREQ": [{"value": "25", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT4_REQUESTED_PHASE": [{"value": "270", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT4_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT5_REQUESTED_OUT_FREQ": [{"value": "40", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT5_REQUESTED_PHASE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT5_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT6_REQUESTED_OUT_FREQ": [{"value": "100", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT6_REQUESTED_PHASE": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT6_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT7_REQUESTED_OUT_FREQ": [{"value": "100.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT7_REQUESTED_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT7_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "user", "format": "float", "usage": "all"}], "USE_MAX_I_JITTER": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_MIN_O_JITTER": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT1_MATCHED_ROUTING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT2_MATCHED_ROUTING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT3_MATCHED_ROUTING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT4_MATCHED_ROUTING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT5_MATCHED_ROUTING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT6_MATCHED_ROUTING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT7_MATCHED_ROUTING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "PRIM_SOURCE": [{"value": "Single_ended_clock_capable_pin", "resolve_type": "user", "usage": "all"}], "CLKOUT1_DRIVES": [{"value": "BUFG", "resolve_type": "user", "usage": "all"}], "CLKOUT2_DRIVES": [{"value": "BUFG", "resolve_type": "user", "usage": "all"}], "CLKOUT3_DRIVES": [{"value": "BUFG", "resolve_type": "user", "usage": "all"}], "CLKOUT4_DRIVES": [{"value": "BUFG", "resolve_type": "user", "usage": "all"}], "CLKOUT5_DRIVES": [{"value": "BUFG", "resolve_type": "user", "usage": "all"}], "CLKOUT6_DRIVES": [{"value": "BUFG", "resolve_type": "user", "usage": "all"}], "CLKOUT7_DRIVES": [{"value": "BUFG", "resolve_type": "user", "usage": "all"}], "FEEDBACK_SOURCE": [{"value": "FDBK_AUTO", "resolve_type": "user", "usage": "all"}], "CLKFB_IN_SIGNALING": [{"value": "SINGLE", "resolve_type": "user", "usage": "all"}], "CLKFB_IN_PORT": [{"value": "clkfb_in", "resolve_type": "user", "usage": "all"}], "CLKFB_IN_P_PORT": [{"value": "clkfb_in_p", "resolve_type": "user", "usage": "all"}], "CLKFB_IN_N_PORT": [{"value": "clkfb_in_n", "resolve_type": "user", "usage": "all"}], "CLKFB_OUT_PORT": [{"value": "clkfb_out", "resolve_type": "user", "usage": "all"}], "CLKFB_OUT_P_PORT": [{"value": "clkfb_out_p", "resolve_type": "user", "usage": "all"}], "CLKFB_OUT_N_PORT": [{"value": "clkfb_out_n", "resolve_type": "user", "usage": "all"}], "PLATFORM": [{"value": "UNKNOWN", "resolve_type": "user", "usage": "all"}], "SUMMARY_STRINGS": [{"value": "empty", "resolve_type": "user", "usage": "all"}], "USE_LOCKED": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "CALC_DONE": [{"value": "empty", "resolve_type": "user", "usage": "all"}], "USE_RESET": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_POWER_DOWN": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_STATUS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_FREEZE": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_CLK_VALID": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_INCLK_STOPPED": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_CLKFB_STOPPED": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "RESET_PORT": [{"value": "reset", "value_src": "user", "resolve_type": "user", "usage": "all"}], "LOCKED_PORT": [{"value": "locked", "value_src": "user", "resolve_type": "user", "usage": "all"}], "POWER_DOWN_PORT": [{"value": "power_down", "resolve_type": "user", "usage": "all"}], "CLK_VALID_PORT": [{"value": "CLK_VALID", "resolve_type": "user", "usage": "all"}], "STATUS_PORT": [{"value": "STATUS", "resolve_type": "user", "usage": "all"}], "CLK_IN_SEL_PORT": [{"value": "clk_in_sel", "resolve_type": "user", "usage": "all"}], "INPUT_CLK_STOPPED_PORT": [{"value": "input_clk_stopped", "resolve_type": "user", "usage": "all"}], "CLKFB_STOPPED_PORT": [{"value": "clkfb_stopped", "resolve_type": "user", "usage": "all"}], "SS_MODE": [{"value": "CENTER_HIGH", "resolve_type": "user", "usage": "all"}], "SS_MOD_FREQ": [{"value": "250", "resolve_type": "user", "format": "float", "usage": "all"}], "SS_MOD_TIME": [{"value": "0.004", "resolve_type": "user", "format": "float", "usage": "all"}], "OVERRIDE_MMCM": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_NOTES": [{"value": "None", "resolve_type": "user", "usage": "all"}], "MMCM_DIVCLK_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "MMCM_BANDWIDTH": [{"value": "OPTIMIZED", "resolve_type": "user", "usage": "all"}], "MMCM_CLKFBOUT_MULT_F": [{"value": "10.000", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKFBOUT_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKFBOUT_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKIN1_PERIOD": [{"value": "10.000", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKIN2_PERIOD": [{"value": "10.000", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT4_CASCADE": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLOCK_HOLD": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_COMPENSATION": [{"value": "ZHOLD", "resolve_type": "user", "usage": "all"}], "MMCM_REF_JITTER1": [{"value": "0.010", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_REF_JITTER2": [{"value": "0.010", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_STARTUP_WAIT": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKOUT0_DIVIDE_F": [{"value": "40.000", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT0_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT0_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT0_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKOUT1_DIVIDE": [{"value": "40", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MMCM_CLKOUT1_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT1_PHASE": [{"value": "90.000", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT1_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKOUT2_DIVIDE": [{"value": "40", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MMCM_CLKOUT2_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT2_PHASE": [{"value": "180.000", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT2_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKOUT3_DIVIDE": [{"value": "40", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MMCM_CLKOUT3_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT3_PHASE": [{"value": "270.000", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT3_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKOUT4_DIVIDE": [{"value": "25", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MMCM_CLKOUT4_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT4_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT4_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKOUT5_DIVIDE": [{"value": "10", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MMCM_CLKOUT5_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT5_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT5_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MMCM_CLKOUT6_DIVIDE": [{"value": "1", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "MMCM_CLKOUT6_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT6_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "MMCM_CLKOUT6_USE_FINE_PS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "OVERRIDE_PLL": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "PLL_NOTES": [{"value": "None", "resolve_type": "user", "usage": "all"}], "PLL_BANDWIDTH": [{"value": "OPTIMIZED", "resolve_type": "user", "usage": "all"}], "PLL_CLKFBOUT_MULT": [{"value": "4", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKFBOUT_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLK_FEEDBACK": [{"value": "CLKFBOUT", "resolve_type": "user", "usage": "all"}], "PLL_DIVCLK_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKIN_PERIOD": [{"value": "10.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_COMPENSATION": [{"value": "SYSTEM_SYNCHRONOUS", "resolve_type": "user", "usage": "all"}], "PLL_REF_JITTER": [{"value": "0.010", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT0_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKOUT0_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT0_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT1_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKOUT1_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT1_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT2_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKOUT2_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT2_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT3_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKOUT3_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT3_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT4_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKOUT4_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT4_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT5_DIVIDE": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "PLL_CLKOUT5_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "user", "format": "float", "usage": "all"}], "PLL_CLKOUT5_PHASE": [{"value": "0.000", "resolve_type": "user", "format": "float", "usage": "all"}], "RESET_TYPE": [{"value": "ACTIVE_HIGH", "value_src": "user", "resolve_type": "user", "usage": "all"}], "USE_SAFE_CLOCK_STARTUP": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_CLOCK_SEQUENCING": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUT1_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "CLKOUT2_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "CLKOUT3_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "CLKOUT4_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "CLKOUT5_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "CLKOUT6_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "CLKOUT7_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "USE_BOARD_FLOW": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLK_IN1_BOARD_INTERFACE": [{"value": "Custom", "resolve_type": "user", "usage": "all"}], "CLK_IN2_BOARD_INTERFACE": [{"value": "Custom", "resolve_type": "user", "usage": "all"}], "DIFF_CLK_IN1_BOARD_INTERFACE": [{"value": "Custom", "resolve_type": "user", "usage": "all"}], "DIFF_CLK_IN2_BOARD_INTERFACE": [{"value": "Custom", "resolve_type": "user", "usage": "all"}], "AUTO_PRIMITIVE": [{"value": "MMCM", "resolve_type": "user", "usage": "all"}], "RESET_BOARD_INTERFACE": [{"value": "Custom", "resolve_type": "user", "usage": "all"}], "ENABLE_CDDC": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CDDCDONE_PORT": [{"value": "cddcdone", "resolve_type": "user", "usage": "all"}], "CDDCREQ_PORT": [{"value": "cddcreq", "resolve_type": "user", "usage": "all"}], "ENABLE_CLKOUTPHY": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CLKOUTPHY_REQUESTED_FREQ": [{"value": "600.000", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT1_JITTER": [{"value": "175.402", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT1_PHASE_ERROR": [{"value": "98.575", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT2_JITTER": [{"value": "175.402", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT2_PHASE_ERROR": [{"value": "98.575", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT3_JITTER": [{"value": "175.402", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT3_PHASE_ERROR": [{"value": "98.575", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT4_JITTER": [{"value": "175.402", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT4_PHASE_ERROR": [{"value": "98.575", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT5_JITTER": [{"value": "159.371", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT5_PHASE_ERROR": [{"value": "98.575", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT6_JITTER": [{"value": "130.958", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT6_PHASE_ERROR": [{"value": "98.575", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT7_JITTER": [{"value": "144.977", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "CLKOUT7_PHASE_ERROR": [{"value": "363.343", "value_src": "user", "resolve_type": "user", "format": "float", "usage": "all"}], "INPUT_MODE": [{"value": "frequency", "resolve_type": "user", "usage": "all"}], "INTERFACE_SELECTION": [{"value": "Enable_AXI", "resolve_type": "user", "usage": "all"}], "AXI_DRP": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "PHASE_DUTY_CONFIG": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}]}, "model_parameters": {"C_CLKOUT2_USED": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USER_CLK_FREQ0": [{"value": "100.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_AUTO_PRIMITIVE": [{"value": "MMCM", "resolve_type": "generated", "usage": "all"}], "C_USER_CLK_FREQ1": [{"value": "100.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_USER_CLK_FREQ2": [{"value": "100.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_USER_CLK_FREQ3": [{"value": "100.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_ENABLE_CLOCK_MONITOR": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_USER_CLOCK0": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_USER_CLOCK1": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_USER_CLOCK2": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_USER_CLOCK3": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_Enable_PLL0": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_Enable_PLL1": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_REF_CLK_FREQ": [{"value": "100.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PRECISION": [{"value": "1", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT3_USED": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT4_USED": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT5_USED": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT6_USED": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT7_USED": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_CLKOUT1_BAR": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_CLKOUT2_BAR": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_CLKOUT3_BAR": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_CLKOUT4_BAR": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "c_component_name": [{"value": "clock_generator", "resolve_type": "generated", "usage": "all"}], "C_PLATFORM": [{"value": "UNKNOWN", "resolve_type": "generated", "usage": "all"}], "C_USE_FREQ_SYNTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_PHASE_ALIGNMENT": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRIM_IN_JITTER": [{"value": "0.010", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_SECONDARY_IN_JITTER": [{"value": "0.010", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_JITTER_SEL": [{"value": "No_Jitter", "resolve_type": "generated", "usage": "all"}], "C_USE_MIN_POWER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_MIN_O_JITTER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_MAX_I_JITTER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_DYN_PHASE_SHIFT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_OPTIMIZE_CLOCKING_STRUCTURE_EN": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_INCLK_SWITCHOVER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_DYN_RECONFIG": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_SPREAD_SPECTRUM": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_FAST_SIMULATION": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRIMTYPE_SEL": [{"value": "AUTO", "resolve_type": "generated", "usage": "all"}], "C_USE_CLK_VALID": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRIM_IN_FREQ": [{"value": "100.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PRIM_IN_TIMEPERIOD": [{"value": "10.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_IN_FREQ_UNITS": [{"value": "Units_MHz", "resolve_type": "generated", "usage": "all"}], "C_SECONDARY_IN_FREQ": [{"value": "100.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_SECONDARY_IN_TIMEPERIOD": [{"value": "10.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_FEEDBACK_SOURCE": [{"value": "FDBK_AUTO", "resolve_type": "generated", "usage": "all"}], "C_PRIM_SOURCE": [{"value": "Single_ended_clock_capable_pin", "resolve_type": "generated", "usage": "all"}], "C_PHASESHIFT_MODE": [{"value": "WAVEFORM", "resolve_type": "generated", "usage": "all"}], "C_SECONDARY_SOURCE": [{"value": "Single_ended_clock_capable_pin", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_IN_SIGNALING": [{"value": "SINGLE", "resolve_type": "generated", "usage": "all"}], "C_USE_RESET": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RESET_LOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_LOCKED": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_INCLK_STOPPED": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_CLKFB_STOPPED": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_POWER_DOWN": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_STATUS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_FREEZE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_NUM_OUT_CLKS": [{"value": "6", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT1_DRIVES": [{"value": "BUFG", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT2_DRIVES": [{"value": "BUFG", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT3_DRIVES": [{"value": "BUFG", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT4_DRIVES": [{"value": "BUFG", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT5_DRIVES": [{"value": "BUFG", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT6_DRIVES": [{"value": "BUFG", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT7_DRIVES": [{"value": "BUFG", "resolve_type": "generated", "usage": "all"}], "C_INCLK_SUM_ROW0": [{"value": "Input Clock   Freq (MHz)    Input Jitter (UI)", "resolve_type": "generated", "usage": "all"}], "C_INCLK_SUM_ROW1": [{"value": "__primary_________100.000____________0.010", "resolve_type": "generated", "usage": "all"}], "C_INCLK_SUM_ROW2": [{"value": "no_secondary_input_clock ", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW0A": [{"value": " Output     Output      Phase    Duty Cycle   Pk-to-Pk     Phase", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW0B": [{"value": "  Clock     Freq (MHz)  (degrees)    (%)     Jitter (ps)  Error (ps)", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW1": [{"value": "controller_phase0__25.00000______0.000______50.0______175.402_____98.575", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW2": [{"value": "controller_phase1__25.00000_____90.000______50.0______175.402_____98.575", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW3": [{"value": "controller_phase2__25.00000____180.000______50.0______175.402_____98.575", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW4": [{"value": "controller_phase3__25.00000____270.000______50.0______175.402_____98.575", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW5": [{"value": "lcd_screen_dclk__40.00000______0.000______50.0______159.371_____98.575", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW6": [{"value": "clock_out__100.00000______0.000______50.0______130.958_____98.575", "resolve_type": "generated", "usage": "all"}], "C_OUTCLK_SUM_ROW7": [{"value": "no_CLK_OUT7_output", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT1_REQUESTED_OUT_FREQ": [{"value": "25", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT2_REQUESTED_OUT_FREQ": [{"value": "25", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT3_REQUESTED_OUT_FREQ": [{"value": "25", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT4_REQUESTED_OUT_FREQ": [{"value": "25", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT5_REQUESTED_OUT_FREQ": [{"value": "40", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT6_REQUESTED_OUT_FREQ": [{"value": "100", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT7_REQUESTED_OUT_FREQ": [{"value": "100.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT1_REQUESTED_PHASE": [{"value": "0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT2_REQUESTED_PHASE": [{"value": "90", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT3_REQUESTED_PHASE": [{"value": "180", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT4_REQUESTED_PHASE": [{"value": "270", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT5_REQUESTED_PHASE": [{"value": "0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT6_REQUESTED_PHASE": [{"value": "0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT7_REQUESTED_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT1_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT2_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT3_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT4_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT5_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT6_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT7_REQUESTED_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT1_OUT_FREQ": [{"value": "25.00000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT2_OUT_FREQ": [{"value": "25.00000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT3_OUT_FREQ": [{"value": "25.00000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT4_OUT_FREQ": [{"value": "25.00000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT5_OUT_FREQ": [{"value": "40.00000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT6_OUT_FREQ": [{"value": "100.00000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT7_OUT_FREQ": [{"value": "100.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT1_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT2_PHASE": [{"value": "90.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT3_PHASE": [{"value": "180.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT4_PHASE": [{"value": "270.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT5_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT6_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT7_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT1_DUTY_CYCLE": [{"value": "50.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT2_DUTY_CYCLE": [{"value": "50.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT3_DUTY_CYCLE": [{"value": "50.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT4_DUTY_CYCLE": [{"value": "50.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT5_DUTY_CYCLE": [{"value": "50.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT6_DUTY_CYCLE": [{"value": "50.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKOUT7_DUTY_CYCLE": [{"value": "50.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_USE_SAFE_CLOCK_STARTUP": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_CLOCK_SEQUENCING": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT1_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT2_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT3_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT4_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT5_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT6_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CLKOUT7_SEQUENCE_NUMBER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_NOTES": [{"value": "None", "resolve_type": "generated", "usage": "all"}], "C_MMCM_BANDWIDTH": [{"value": "OPTIMIZED", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKFBOUT_MULT_F": [{"value": "10.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKIN1_PERIOD": [{"value": "10.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKIN2_PERIOD": [{"value": "10.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT4_CASCADE": [{"value": "FALSE", "resolve_type": "generated", "format": "bool", "usage": "all"}], "C_MMCM_CLOCK_HOLD": [{"value": "FALSE", "resolve_type": "generated", "format": "bool", "usage": "all"}], "C_MMCM_COMPENSATION": [{"value": "ZHOLD", "resolve_type": "generated", "usage": "all"}], "C_MMCM_DIVCLK_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_REF_JITTER1": [{"value": "0.010", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_REF_JITTER2": [{"value": "0.010", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_STARTUP_WAIT": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT0_DIVIDE_F": [{"value": "40.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT1_DIVIDE": [{"value": "40", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_CLKOUT2_DIVIDE": [{"value": "40", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_CLKOUT3_DIVIDE": [{"value": "40", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_CLKOUT4_DIVIDE": [{"value": "25", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_CLKOUT5_DIVIDE": [{"value": "10", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_CLKOUT6_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MMCM_CLKOUT0_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT1_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT2_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT3_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT4_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT5_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT6_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKFBOUT_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT0_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT1_PHASE": [{"value": "90.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT2_PHASE": [{"value": "180.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT3_PHASE": [{"value": "270.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT4_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT5_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKOUT6_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_MMCM_CLKFBOUT_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT0_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT1_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT2_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT3_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT4_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT5_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_MMCM_CLKOUT6_USE_FINE_PS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "C_PLL_NOTES": [{"value": "No notes", "resolve_type": "generated", "usage": "all"}], "C_PLL_BANDWIDTH": [{"value": "OPTIMIZED", "resolve_type": "generated", "usage": "all"}], "C_PLL_CLK_FEEDBACK": [{"value": "CLKFBOUT", "resolve_type": "generated", "usage": "all"}], "C_PLL_CLKFBOUT_MULT": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_CLKIN_PERIOD": [{"value": "1.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_COMPENSATION": [{"value": "SYSTEM_SYNCHRONOUS", "resolve_type": "generated", "usage": "all"}], "C_PLL_DIVCLK_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_REF_JITTER": [{"value": "0.010", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT0_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_CLKOUT1_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_CLKOUT2_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_CLKOUT3_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_CLKOUT4_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_CLKOUT5_DIVIDE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PLL_CLKOUT0_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT1_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT2_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT3_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT4_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT5_DUTY_CYCLE": [{"value": "0.500", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKFBOUT_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT0_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT1_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT2_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT3_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT4_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PLL_CLKOUT5_PHASE": [{"value": "0.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLOCK_MGR_TYPE": [{"value": "NA", "resolve_type": "generated", "usage": "all"}], "C_OVERRIDE_MMCM": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_OVERRIDE_PLL": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRIMARY_PORT": [{"value": "clock_in", "resolve_type": "generated", "usage": "all"}], "C_SECONDARY_PORT": [{"value": "clk_in2", "resolve_type": "generated", "usage": "all"}], "C_CLK_OUT1_PORT": [{"value": "controller_phase0", "resolve_type": "generated", "usage": "all"}], "C_CLK_OUT2_PORT": [{"value": "controller_phase1", "resolve_type": "generated", "usage": "all"}], "C_CLK_OUT3_PORT": [{"value": "controller_phase2", "resolve_type": "generated", "usage": "all"}], "C_CLK_OUT4_PORT": [{"value": "controller_phase3", "resolve_type": "generated", "usage": "all"}], "C_CLK_OUT5_PORT": [{"value": "lcd_screen_dclk", "resolve_type": "generated", "usage": "all"}], "C_CLK_OUT6_PORT": [{"value": "clock_out", "resolve_type": "generated", "usage": "all"}], "C_CLK_OUT7_PORT": [{"value": "clk_out7", "resolve_type": "generated", "usage": "all"}], "C_RESET_PORT": [{"value": "reset", "resolve_type": "generated", "usage": "all"}], "C_LOCKED_PORT": [{"value": "locked", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_IN_PORT": [{"value": "clkfb_in", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_IN_P_PORT": [{"value": "clkfb_in_p", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_IN_N_PORT": [{"value": "clkfb_in_n", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_OUT_PORT": [{"value": "clkfb_out", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_OUT_P_PORT": [{"value": "clkfb_out_p", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_OUT_N_PORT": [{"value": "clkfb_out_n", "resolve_type": "generated", "usage": "all"}], "C_POWER_DOWN_PORT": [{"value": "power_down", "resolve_type": "generated", "usage": "all"}], "C_DADDR_PORT": [{"value": "daddr", "resolve_type": "generated", "usage": "all"}], "C_DCLK_PORT": [{"value": "dclk", "resolve_type": "generated", "usage": "all"}], "C_DRDY_PORT": [{"value": "drdy", "resolve_type": "generated", "usage": "all"}], "C_DWE_PORT": [{"value": "dwe", "resolve_type": "generated", "usage": "all"}], "C_DIN_PORT": [{"value": "din", "resolve_type": "generated", "usage": "all"}], "C_DOUT_PORT": [{"value": "dout", "resolve_type": "generated", "usage": "all"}], "C_DEN_PORT": [{"value": "den", "resolve_type": "generated", "usage": "all"}], "C_PSCLK_PORT": [{"value": "psclk", "resolve_type": "generated", "usage": "all"}], "C_PSEN_PORT": [{"value": "psen", "resolve_type": "generated", "usage": "all"}], "C_PSINCDEC_PORT": [{"value": "psincdec", "resolve_type": "generated", "usage": "all"}], "C_PSDONE_PORT": [{"value": "psdone", "resolve_type": "generated", "usage": "all"}], "C_CLK_VALID_PORT": [{"value": "CLK_VALID", "resolve_type": "generated", "usage": "all"}], "C_STATUS_PORT": [{"value": "STATUS", "resolve_type": "generated", "usage": "all"}], "C_CLK_IN_SEL_PORT": [{"value": "clk_in_sel", "resolve_type": "generated", "usage": "all"}], "C_INPUT_CLK_STOPPED_PORT": [{"value": "input_clk_stopped", "resolve_type": "generated", "usage": "all"}], "C_CLKFB_STOPPED_PORT": [{"value": "clkfb_stopped", "resolve_type": "generated", "usage": "all"}], "C_CLKIN1_JITTER_PS": [{"value": "100.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_CLKIN2_JITTER_PS": [{"value": "100.0", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_PRIMITIVE": [{"value": "MMCM", "resolve_type": "generated", "usage": "all"}], "C_SS_MODE": [{"value": "CENTER_HIGH", "resolve_type": "generated", "usage": "all"}], "C_SS_MOD_PERIOD": [{"value": "4000", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SS_MOD_TIME": [{"value": "0.004", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_HAS_CDDC": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CDDCDONE_PORT": [{"value": "cddcdone", "resolve_type": "generated", "usage": "all"}], "C_CDDCREQ_PORT": [{"value": "cddcreq", "resolve_type": "generated", "usage": "all"}], "C_CLKOUTPHY_MODE": [{"value": "VCO", "resolve_type": "generated", "usage": "all"}], "C_ENABLE_CLKOUTPHY": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_INTERFACE_SELECTION": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_AXI_ADDR_WIDTH": [{"value": "11", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_S_AXI_DATA_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_POWER_REG": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT0_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT0_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT1_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT1_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT2_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT2_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT3_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT3_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT4_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT4_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT5_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT5_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT6_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT6_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKFBOUT_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_CLKFBOUT_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_DIVCLK": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_LOCK_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_LOCK_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_LOCK_3": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_FILTER_1": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_FILTER_2": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "C_DIVIDE1_AUTO": [{"value": "1", "resolve_type": "generated", "usage": "all"}], "C_DIVIDE2_AUTO": [{"value": "1.0", "resolve_type": "generated", "usage": "all"}], "C_DIVIDE3_AUTO": [{"value": "1.0", "resolve_type": "generated", "usage": "all"}], "C_DIVIDE4_AUTO": [{"value": "1.0", "resolve_type": "generated", "usage": "all"}], "C_DIVIDE5_AUTO": [{"value": "0.625", "resolve_type": "generated", "usage": "all"}], "C_DIVIDE6_AUTO": [{"value": "0.25", "resolve_type": "generated", "usage": "all"}], "C_DIVIDE7_AUTO": [{"value": "0.025", "resolve_type": "generated", "usage": "all"}], "C_PLLBUFGCEDIV": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_PLLBUFGCEDIV1": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_PLLBUFGCEDIV2": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_PLLBUFGCEDIV3": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_PLLBUFGCEDIV4": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV1": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV2": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV3": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV4": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV5": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV6": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_MMCMBUFGCEDIV7": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT1_MATCHED_ROUTING": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT2_MATCHED_ROUTING": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT3_MATCHED_ROUTING": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT4_MATCHED_ROUTING": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT5_MATCHED_ROUTING": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT6_MATCHED_ROUTING": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT7_MATCHED_ROUTING": [{"value": "false", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT0_ACTUAL_FREQ": [{"value": "25.00000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT1_ACTUAL_FREQ": [{"value": "25.00000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT2_ACTUAL_FREQ": [{"value": "25.00000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT3_ACTUAL_FREQ": [{"value": "25.00000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT4_ACTUAL_FREQ": [{"value": "40.00000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT5_ACTUAL_FREQ": [{"value": "100.00000", "resolve_type": "generated", "usage": "all"}], "C_CLKOUT6_ACTUAL_FREQ": [{"value": "100.000", "resolve_type": "generated", "usage": "all"}], "C_M_MAX": [{"value": "64.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_M_MIN": [{"value": "2.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_D_MAX": [{"value": "80.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_D_MIN": [{"value": "1.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_O_MAX": [{"value": "128.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_O_MIN": [{"value": "1.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_VCO_MIN": [{"value": "600.000", "resolve_type": "generated", "format": "float", "usage": "all"}], "C_VCO_MAX": [{"value": "1200.000", "resolve_type": "generated", "format": "float", "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "artix7"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7a100t"}], "NEXTGEN_VERSAL": [{"value": "0"}], "PACKAGE": [{"value": "fgg484"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-1"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Flow"}], "IPREVISION": [{"value": "14"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "../../../../advanced1Snake.gen/sources_1/ip/clock_generator"}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "."}], "SWVERSION": [{"value": "2024.1"}], "SYNTHESISFLOW": [{"value": "GLOBAL"}]}}, "boundary": {"ports": {"reset": [{"direction": "in", "driver_value": "0"}], "clock_in": [{"direction": "in"}], "controller_phase0": [{"direction": "out"}], "controller_phase1": [{"direction": "out"}], "controller_phase2": [{"direction": "out"}], "controller_phase3": [{"direction": "out"}], "lcd_screen_dclk": [{"direction": "out"}], "clock_out": [{"direction": "out"}], "locked": [{"direction": "out"}]}, "interfaces": {"reset": {"vlnv": "xilinx.com:signal:reset:1.0", "abstraction_type": "xilinx.com:signal:reset_rtl:1.0", "mode": "slave", "parameters": {"POLARITY": [{"value": "ACTIVE_HIGH", "value_src": "constant", "usage": "all"}], "BOARD.ASSOCIATED_PARAM": [{"value": "RESET_BOARD_INTERFACE", "value_src": "constant", "usage": "all"}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"RST": [{"physical_name": "reset"}]}}, "clock_CLK_IN1": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}], "BOARD.ASSOCIATED_PARAM": [{"value": "CLK_IN1_BOARD_INTERFACE", "usage": "all", "is_static_object": false}]}, "port_maps": {"CLK_IN1": [{"physical_name": "clock_in"}]}}, "clock_CLK_OUT1": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "master", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK_OUT1": [{"physical_name": "controller_phase0"}]}}, "clock_CLK_OUT2": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "master", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK_OUT2": [{"physical_name": "controller_phase1"}]}}, "clock_CLK_OUT3": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "master", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK_OUT3": [{"physical_name": "controller_phase2"}]}}, "clock_CLK_OUT4": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "master", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK_OUT4": [{"physical_name": "controller_phase3"}]}}, "clock_CLK_OUT5": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "master", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK_OUT5": [{"physical_name": "lcd_screen_dclk"}]}}, "clock_CLK_OUT6": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "master", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK_OUT6": [{"physical_name": "clock_out"}]}}}}}}