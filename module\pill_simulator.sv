parameter pill_simulator_frequency = 100000000;

module pill_simulator(
    input logic clock,
    input logic reset_n,
    input logic strict_enable,
    input logic pill_disable,
    input logic [1:0] speed_select,  // 0=slow(2/s), 1=mid(5/s), 2=fast(10/s)
    output logic pill_pulse
);

    logic output_enable;
    assign output_enable = strict_enable || ~ pill_disable;

    // Speed configuration
    logic [31:0] pill_per_second;
    always_comb begin
        case (speed_select)
            2'b00: pill_per_second = 2;   // slow
            2'b01: pill_per_second = 5;   // mid
            2'b10: pill_per_second = 10;  // fast
            default: pill_per_second = 5; // default to mid
        endcase
    end

    // Variable frequency divider
    logic [31:0] divisor;
    assign divisor = pill_simulator_frequency / pill_per_second;

    logic pill_signal;
    v_divider #(32) pill_divider_ins (
        .clock(clock),
        .reset_n(reset_n),
        .divisor(divisor),
        .clock_out(pill_signal)
    );

    assign pill_pulse = output_enable ? pill_signal : 0;

endmodule
