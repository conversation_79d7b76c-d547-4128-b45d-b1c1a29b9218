; Sample COE file with 8-bit data width
memory_initialization_radix=2;
memory_initialization_vector=
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01000001,
00100001,
00100001,
00100001,
00100001,
00100001,
00100001,
00100001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01000001,
01101010,
10001010,
10001010,
10110010,
11010010,
11010010,
11010010,
11010010,
10110010,
10001010,
01101010,
01001001,
00100001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
01000001,
01101010,
10001010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10110010,
10001010,
01101001,
00100001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01001010,
10001010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
01101010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01101010,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01101010,
10110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
01101010,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01000001,
00000000,
00000000,
00000000,
00000000,
00000000,
01000001,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
00100001,
00000000,
00000000,
00000000,
00100001,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
01101010,
00000000,
00000000,
00000000,
01001010,
10110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11110010,
11010010,
11010010,
11010010,
11010010,
11110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01000001,
00000000,
00100000,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11001010,
10101001,
10100001,
10100010,
10100001,
10100001,
10101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
01101001,
00000000,
01000001,
10001010,
11111010,
11111010,
11110010,
11110010,
11110010,
11110010,
11111010,
11111010,
11111010,
10101010,
10100001,
10101010,
11010010,
11010010,
11010010,
11001010,
10101010,
10100010,
11010010,
11111010,
11111010,
11111010,
11110010,
11110010,
11110010,
11111010,
11111010,
11111010,
10001010,
00100001,
01000001,
10110010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11010010,
10100001,
11001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11110010,
10101010,
10101010,
11110010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11111010,
10110010,
00100001,
01101010,
11010010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11010010,
10100001,
11110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
10100001,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11010001,
01101010,
01101001,
11111010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111010,
11111010,
11110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11110010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11111001,
11110001,
01101010,
01101001,
11111010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
11110010,
11010010,
11011010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
11110010,
11110010,
11110010,
11110010,
11010010,
01110100,
01010100,
01110100,
10010011,
01101001,
01101001,
11111010,
11111010,
11110010,
11110010,
11110010,
11110010,
11110010,
10110010,
01101001,
01101001,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10001010,
01000001,
01101001,
11110010,
11110010,
11010010,
00110101,
00110111,
00110111,
00110111,
00110110,
00101100,
01101001,
11111010,
11111010,
11111010,
11110010,
11110010,
11110010,
11111010,
10001010,
01000001,
01000001,
10010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
01101001,
01000001,
01000001,
11010010,
11110010,
00110101,
00110111,
00110110,
01010110,
00110110,
00110111,
00110110,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
10001001,
10001001,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10110010,
01101001,
10001010,
11111010,
10110011,
00010110,
00110110,
01011111,
01111111,
01111111,
01010110,
00110110,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10010011,
00110110,
01111111,
10011111,
10011111,
10011111,
10011111,
01010110,
01000001,
10110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10010011,
00110111,
10011111,
10011111,
10011111,
10011111,
10011111,
01111111,
00100001,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10110011,
00110110,
10011111,
10011111,
10011111,
10011111,
10011111,
01111111,
00000000,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11011010,
01010110,
10011111,
10011111,
10011111,
10011111,
10011111,
01111111,
00000000,
01000001,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
01010100,
10111111,
11011111,
10011111,
10011111,
10011111,
01010110,
00000000,
00100000,
01101010,
10110010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10110011,
01110111,
11011111,
10011111,
10011111,
10011111,
01010101,
00000000,
00000000,
00100001,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111001,
01110101,
11111111,
11011111,
10011111,
01111111,
00000010,
00000000,
00000000,
00000000,
01000001,
10001010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111001,
10010011,
10011111,
11111111,
10011111,
01010101,
00000000,
00000000,
00000000,
00000000,
00000000,
01001010,
10001010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
01101001,
01010101,
10111111,
01111111,
00000010,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
01000010,
01101010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
10110010,
01101010,
01000001,
00101010,
01011111,
01010101,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
01000001,
01101010,
10001010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
10001010,
01101010,
00100001,
00000000,
00000000,
00101100,
00000010,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100001,
01000010,
01101010,
10001010,
10110010,
11010010,
11111010,
11111010,
11111010,
11111010,
11111010,
11111010,
11010010,
10110010,
10001010,
01101010,
01000001,
00100000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00100000,
01000001,
01001010,
01101010,
01101001,
01101010,
10001010,
10001010,
01101001,
01101001,
01101010,
01000001,
00100001,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000,
00000000;
